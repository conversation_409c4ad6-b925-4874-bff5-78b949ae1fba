import React, { useState, useRef, useEffect } from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { useData } from '../contexts/DataContext';
import { Product } from '../types';

interface CanvasScreenProps {
  onNavigate?: (screen: string) => void;
}

const CanvasScreen: React.FC<CanvasScreenProps> = ({ onNavigate }) => {
  const { isDark } = useTheme();
  const { state, saveCanvas, updateCurrentCanvas } = useData();

  // Get canvas products from current canvas or create empty array
  const canvasProducts = state.currentCanvas?.products || [];

  const handleProductAdd = async (product: Product, position: { x: number; y: number }) => {
    try {
      // Ensure canvas is initialized
      let canvasId: string;
      let canvasName: string;
      let existingConnections: any[] = [];

      if (state.currentCanvas) {
        canvasId = state.currentCanvas.id;
        canvasName = state.currentCanvas.name;
        existingConnections = state.currentCanvas.connections || [];
      } else {
        // Initialize default canvas if it doesn't exist
        canvasId = 'default';
        canvasName = 'My Canvas';
        existingConnections = [];

        // Create initial canvas state
        const initialCanvas = {
          id: canvasId,
          name: canvasName,
          products: [],
          connections: [],
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        };

        // Save the initial canvas first
        await saveCanvas(initialCanvas);
        updateCurrentCanvas(initialCanvas);
      }

      const newCanvasProduct = {
        ...product,
        position,
        canvasId,
      };

      const updatedProducts = [...canvasProducts, newCanvasProduct];
      const updatedCanvas = {
        id: canvasId,
        name: canvasName,
        products: updatedProducts,
        connections: existingConnections,
      };

      await saveCanvas(updatedCanvas);
      updateCurrentCanvas({
        ...updatedCanvas,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      // Success message removed - operations now silent
    } catch (error) {
      alert('Error: Failed to add product to canvas');
      console.error('Error adding product:', error);
    }
  };

  const handleProductMove = async (productId: string, position: { x: number; y: number }) => {
    try {
      // Ensure canvas exists before moving products
      if (!state.currentCanvas) {
        console.warn('No canvas available for product move');
        return;
      }

      const updatedProducts = canvasProducts.map(cp =>
        cp.id === productId ? { ...cp, position } : cp
      );

      const canvasId: string = state.currentCanvas.id;
      const updatedCanvas = {
        id: canvasId,
        name: state.currentCanvas.name,
        products: updatedProducts,
        connections: state.currentCanvas.connections || [],
      };

      await saveCanvas(updatedCanvas);
      updateCurrentCanvas({
        ...updatedCanvas,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error moving product:', error);
    }
  };

  const handleProductRemove = async (productId: string) => {
    try {
      // Ensure canvas exists before removing products
      if (!state.currentCanvas) {
        alert('Error: No canvas available');
        return;
      }

      const updatedProducts = canvasProducts.filter(cp => cp.id !== productId);
      const updatedConnections = (state.currentCanvas.connections || []).filter(
        conn => conn.from !== productId && conn.to !== productId
      );

      const canvasId: string = state.currentCanvas.id;
      const updatedCanvas = {
        id: canvasId,
        name: state.currentCanvas.name,
        products: updatedProducts,
        connections: updatedConnections,
      };

      await saveCanvas(updatedCanvas);
      updateCurrentCanvas({
        ...updatedCanvas,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });

      // Success message removed - operations now silent
    } catch (error) {
      alert('Error: Failed to remove product');
      console.error('Error removing product:', error);
    }
  };

  const handleProductRotate = async (productId: string, rotation: number) => {
    try {
      // Ensure canvas exists before rotating products
      if (!state.currentCanvas) {
        console.warn('No canvas available for product rotation');
        return;
      }

      const updatedProducts = canvasProducts.map(cp =>
        cp.id === productId ? { ...cp, rotation } : cp
      );

      const canvasId: string = state.currentCanvas.id;
      const updatedCanvas = {
        id: canvasId,
        name: state.currentCanvas.name,
        products: updatedProducts,
        connections: state.currentCanvas.connections || [],
      };

      await saveCanvas(updatedCanvas);
      updateCurrentCanvas({
        ...updatedCanvas,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error rotating product:', error);
    }
  };

  const handleToggleBackground = (productId: string) => {
    setProductsWithoutBackground(prev => {
      const newSet = new Set(prev);
      if (newSet.has(productId)) {
        newSet.delete(productId);
      } else {
        newSet.add(productId);
      }
      return newSet;
    });
  };

  const containerStyle: React.CSSProperties = {
    minHeight: '100vh',
    backgroundColor: isDark ? '#0f172a' : '#f9fafb',
    padding: '0 20px 20px 20px',
  };

  const headerStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#1e293b' : '#ffffff',
    padding: '16px',
    borderRadius: '12px',
    marginBottom: '20px',
    boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  };

  const titleStyle: React.CSSProperties = {
    fontSize: '24px',
    fontWeight: 'bold',
    margin: '0 0 8px 0',
    color: isDark ? '#f1f5f9' : '#1e293b',
  };

  const subtitleStyle: React.CSSProperties = {
    fontSize: '14px',
    margin: 0,
    color: isDark ? '#94a3b8' : '#6b7280',
  };

  const canvasStyle: React.CSSProperties = {
    backgroundColor: isDark ? '#0f172a' : '#ffffff',
    border: `2px dashed ${isDark ? '#475569' : '#d1d5db'}`,
    position: 'fixed',
    top: 0,
    left: 0,
    width: '100vw',
    height: '100vh',
    overflow: 'hidden',
    transition: 'all 0.3s ease',
    userSelect: 'none', // Prevent text selection during drag
    WebkitUserSelect: 'none', // Safari support
    MozUserSelect: 'none', // Firefox support
    msUserSelect: 'none', // IE support
    touchAction: 'manipulation', // Optimize for touch interactions
    WebkitTouchCallout: 'none', // Disable callout on iOS
    WebkitTapHighlightColor: 'transparent', // Remove tap highlight
  };

  const [isDragOver, setIsDragOver] = React.useState(false);
  const [productsWithoutBackground, setProductsWithoutBackground] = React.useState<Set<string>>(new Set());
  const [draggedProduct, setDraggedProduct] = React.useState<string | null>(null);
  const [dragOffset, setDragOffset] = React.useState({ x: 0, y: 0 });
  const [dragPreview, setDragPreview] = React.useState<{ x: number; y: number } | null>(null);
  const [isTouchDragging, setIsTouchDragging] = React.useState(false);
  const [touchStartTime, setTouchStartTime] = React.useState<number>(0);
  const [isMouseDragging, setIsMouseDragging] = React.useState(false);
  const [mouseStartPos, setMouseStartPos] = React.useState({ x: 0, y: 0 });
  const [isRotating, setIsRotating] = React.useState(false);
  const [rotatingProduct, setRotatingProduct] = React.useState<string | null>(null);
  const [initialRotation, setInitialRotation] = React.useState(0);
  const [initialTouchAngle, setInitialTouchAngle] = React.useState(0);
  const [rotationCenter, setRotationCenter] = React.useState({ x: 0, y: 0 });
  const [hoveredProduct, setHoveredProduct] = React.useState<string | null>(null);
  const canvasRef = React.useRef<HTMLDivElement>(null);
  const animationFrameRef = React.useRef<number | null>(null);

  // Direct rotation update function for smooth performance
  const updateRotation = React.useCallback((clientX: number, clientY: number) => {
    if (!isRotating || !rotatingProduct) return;

    const currentAngle = Math.atan2(clientY - rotationCenter.y, clientX - rotationCenter.x) * 180 / Math.PI;
    const deltaAngle = currentAngle - initialTouchAngle;
    const newRotation = Math.round((initialRotation + deltaAngle) * 100) / 100; // Round to 2 decimal places

    // Use hardware-accelerated transform for smooth rotation
    const productElement = document.querySelector(`[data-product-id="${rotatingProduct}"]`) as HTMLElement;
    if (productElement) {
      productElement.style.transform = `translateZ(0) rotate(${newRotation}deg)`;
      productElement.style.willChange = 'transform';
    }

    console.log('Smooth rotation - angle:', currentAngle, 'delta:', deltaAngle, 'rotation:', newRotation);
  }, [isRotating, rotatingProduct, rotationCenter, initialTouchAngle, initialRotation]);

  // Start rotation with animation frame
  const startRotation = React.useCallback((clientX: number, clientY: number, centerX: number, centerY: number, productId: string, currentRotation: number) => {
    const angle = Math.atan2(clientY - centerY, clientX - centerX) * 180 / Math.PI;

    setInitialTouchAngle(angle);
    setInitialRotation(currentRotation);
    setRotationCenter({ x: centerX, y: centerY });
    setRotatingProduct(productId);
    setIsRotating(true);

    console.log('Rotation started - initial angle:', angle, 'center:', { x: centerX, y: centerY });
  }, []);

  // Stop rotation with proper cleanup
  const stopRotation = React.useCallback(async (clientX: number, clientY: number) => {
    if (!isRotating || !rotatingProduct) return;

    // Calculate final rotation
    const finalAngle = Math.atan2(clientY - rotationCenter.y, clientX - rotationCenter.x) * 180 / Math.PI;
    const deltaAngle = finalAngle - initialTouchAngle;
    const finalRotation = Math.round((initialRotation + deltaAngle) * 100) / 100; // Round to 2 decimal places

    console.log('Rotation stopped - final rotation:', finalRotation);

    // Update final rotation and reset hardware acceleration
    const productElement = document.querySelector(`[data-product-id="${rotatingProduct}"]`) as HTMLElement;
    if (productElement) {
      productElement.style.transform = `rotate(${finalRotation}deg)`;
      productElement.style.willChange = 'auto';
    }

    // Reset visual feedback for button
    const rotationButton = document.querySelector(`[data-rotation-handle="${rotatingProduct}"]`) as HTMLElement;
    if (rotationButton) {
      rotationButton.style.transform = 'scale(1)';
    }

    // Save the rotation
    await handleProductRotate(rotatingProduct, finalRotation);

    // Reset state
    setIsRotating(false);
    setRotatingProduct(null);
  }, [isRotating, rotatingProduct, rotationCenter, initialTouchAngle, initialRotation, handleProductRotate]);

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);

    try {
      const productData = e.dataTransfer.getData('application/json');
      if (productData && canvasRef.current) {
        const product: Product = JSON.parse(productData);

        // Calculate drop position relative to canvas
        const canvasRect = canvasRef.current.getBoundingClientRect();
        const dropX = e.clientX - canvasRect.left;
        const dropY = e.clientY - canvasRect.top;

        // Ensure position is within canvas bounds
        const position = {
          x: Math.max(0, Math.min(dropX, canvasRect.width - 120 - 20)), // 120px is approx product width + 20px padding
          y: Math.max(0, Math.min(dropY, canvasRect.height - 140 - 20)), // 140px is approx product height + 20px padding
        };

        await handleProductAdd(product, position);
      }
    } catch (error) {
      console.error('Error handling drop:', error);
      alert('Error: Failed to add product to canvas');
    }
  };

  const dragOverStyle: React.CSSProperties = {
    ...canvasStyle,
    borderColor: isDragOver ? '#3b82f6' : (isDark ? '#374151' : '#d1d5db'),
    backgroundColor: isDragOver
      ? (isDark ? 'rgba(59, 130, 246, 0.1)' : 'rgba(59, 130, 246, 0.05)')
      : (isDark ? '#0f172a' : '#ffffff'),
  };

  return (
    <div style={containerStyle}>

      <div
        ref={canvasRef}
        style={isDragOver ? dragOverStyle : canvasStyle}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onMouseMove={(e) => {
          // Handle mouse move for rotation (prioritize over dragging)
          if (isRotating && rotatingProduct) {
            e.preventDefault();
            updateRotation(e.clientX, e.clientY);
          }
          // Handle mouse move for fallback drag mechanism (only if not rotating)
          else if (isMouseDragging && draggedProduct && canvasRef.current) {
            e.preventDefault();

            const canvasRect = canvasRef.current.getBoundingClientRect();
            const newX = e.clientX - canvasRect.left - dragOffset.x;
            const newY = e.clientY - canvasRect.top - dragOffset.y;

            // Constrain to canvas bounds
            const constrainedX = Math.max(0, Math.min(newX, canvasRect.width - 200));
            const constrainedY = Math.max(0, Math.min(newY, canvasRect.height - 180));

            // Find the dragged product element and update its position
            const productElement = document.querySelector(`[data-product-id="${draggedProduct}"]`) as HTMLElement;
            if (productElement) {
              productElement.style.left = `${constrainedX}px`;
              productElement.style.top = `${constrainedY}px`;
            }
          }
        }}
        onMouseUp={async (e) => {
          // Handle mouse up for rotation (prioritize over dragging)
          if (isRotating && rotatingProduct) {
            e.preventDefault();
            await stopRotation(e.clientX, e.clientY);
          }
          // Handle mouse up for fallback drag mechanism (only if not rotating)
          else if (isMouseDragging && draggedProduct && canvasRef.current) {
            e.preventDefault();

            const canvasRect = canvasRef.current.getBoundingClientRect();
            const newX = e.clientX - canvasRect.left - dragOffset.x;
            const newY = e.clientY - canvasRect.top - dragOffset.y;

            const position = {
              x: Math.max(0, Math.min(newX, canvasRect.width - 200)),
              y: Math.max(0, Math.min(newY, canvasRect.height - 180)),
            };

            // Reset visual feedback
            const productElement = document.querySelector(`[data-product-id="${draggedProduct}"]`) as HTMLElement;
            if (productElement) {
              productElement.style.transform = 'scale(1) rotate(0deg)';
              productElement.style.zIndex = '1';
              productElement.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';
            }

            console.log('Mouse drag ended for product:', draggedProduct, 'final position:', position);
            await handleProductMove(draggedProduct, position);
          }

          // Reset mouse drag state
          setIsMouseDragging(false);
          setDraggedProduct(null);
          setDragOffset({ x: 0, y: 0 });
          setMouseStartPos({ x: 0, y: 0 });

          // Reset rotation state
          setIsRotating(false);
          setRotatingProduct(null);
        }}
        onMouseLeave={() => {
          // Cancel drag if mouse leaves canvas
          if (isMouseDragging) {
            console.log('Mouse left canvas, cancelling drag');
            setIsMouseDragging(false);
            setDraggedProduct(null);
            setDragOffset({ x: 0, y: 0 });
            setMouseStartPos({ x: 0, y: 0 });
          }
        }}
      >
        {canvasProducts.length === 0 ? (
          <div style={{
            textAlign: 'center',
            color: 'rgb(107, 114, 128)',
            padding: '40px 20px',
            width: '100%'
          }}>
            <h2 style={{ fontSize: '48px', marginBottom: '16px' }}>🎨</h2>
            <h3 style={{ margin: '0px 0px 8px', color: 'rgb(30, 41, 59)' }}>
              Your Canvas is Empty
            </h3>
            <p>Drag products from the sidebar to add them to your canvas!</p>
            <p style={{ fontSize: '14px', marginTop: '10px' }}>
              💡 Tip: Drag products anywhere on the canvas for free positioning
            </p>
            <p style={{ fontSize: '14px', marginTop: '5px', color: '#6b7280' }}>
              📱 Mobile: Touch and hold a product to drag it around the canvas
            </p>
            {isDragOver && (
              <div style={{
                marginTop: '20px',
                padding: '20px',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderRadius: '8px',
                border: '2px solid #3b82f6',
              }}>
                <p style={{ color: '#3b82f6', fontWeight: 'bold', margin: 0 }}>
                  🎯 Drop product here!
                </p>
              </div>
            )}
          </div>
        ) : (
          <>
            {/* Products positioned absolutely on the canvas */}
            {canvasProducts.map((product) => (
              <div
                key={product.id}
                data-product-id={product.id}
                draggable={true}
                onMouseEnter={() => setHoveredProduct(product.id)}
                onMouseLeave={() => {
                  // Instant hide for maximum responsiveness
                  console.log('Mouse left product - hiding buttons instantly');
                  setHoveredProduct(null);
                }}
                onTouchStart={(e) => {
                  // For mobile devices, show buttons on touch
                  if (!hoveredProduct) {
                    setHoveredProduct(product.id);
                    // Auto-hide after 3 seconds on mobile
                    setTimeout(() => setHoveredProduct(null), 3000);
                  }

                  // Handle touch start for mobile devices
                  e.stopPropagation();

                  const touch = e.touches[0];
                  if (touch && !draggedProduct && !isRotating &&
                      (e.target === e.currentTarget || !(e.target as HTMLElement).closest('button'))) { // Only start if not already dragging or rotating and not on a button
                    setTouchStartTime(Date.now());
                    setDraggedProduct(product.id);
                    setIsTouchDragging(true);

                    // Calculate touch offset more accurately
                    const rect = e.currentTarget.getBoundingClientRect();
                    const offsetX = touch.clientX - rect.left;
                    const offsetY = touch.clientY - rect.top;

                    setDragOffset({ x: offsetX, y: offsetY });

                    // Add visual feedback for touch start
                    e.currentTarget.style.transform = 'scale(1.05) rotate(1deg)';
                    e.currentTarget.style.zIndex = '1000';
                    e.currentTarget.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.3)';

                    // Provide haptic feedback if available
                    if ('vibrate' in navigator) {
                      navigator.vibrate(50);
                    }

                    console.log('Touch started for product:', product.name, 'offset:', { x: offsetX, y: offsetY });
                  }
                }}
                onDragStart={(e) => {
                  // Prevent mouse drag if we're in a touch interaction
                  if (isTouchDragging) {
                    console.log('Preventing mouse drag - touch interaction active');
                    e.preventDefault();
                    return;
                  }

                  console.log('Mouse drag started for product:', product.name);
                  setDraggedProduct(product.id);
                  e.dataTransfer.setData('application/json', JSON.stringify(product));
                  e.dataTransfer.effectAllowed = 'move';

                  // Calculate drag offset for precise positioning
                  const rect = e.currentTarget.getBoundingClientRect();
                  const offsetX = e.clientX - rect.left;
                  const offsetY = e.clientY - rect.top;
                  setDragOffset({ x: offsetX, y: offsetY });

                  console.log('Mouse drag offset calculated:', { x: offsetX, y: offsetY });

                  // Add visual feedback for drag start
                  e.currentTarget.style.opacity = '0.8';
                  e.currentTarget.style.transform = 'scale(1.05) rotate(2deg)';
                  e.currentTarget.style.zIndex = '1000';

                  // Create a custom drag image
                  try {
                    const dragImage = e.currentTarget.cloneNode(true) as HTMLElement;
                    dragImage.style.opacity = '0.5';
                    dragImage.style.transform = 'scale(0.8)';
                    dragImage.style.position = 'absolute';
                    dragImage.style.top = '-1000px';
                    document.body.appendChild(dragImage);
                    e.dataTransfer.setDragImage(dragImage, offsetX, offsetY);

                    // Remove the temporary drag image after a short delay
                    setTimeout(() => {
                      if (document.body.contains(dragImage)) {
                        document.body.removeChild(dragImage);
                      }
                    }, 0);
                  } catch (error) {
                    console.warn('Failed to create drag image:', error);
                  }
                }}
                onDrag={async (e) => {
                  // Prevent mouse drag updates if we're in a touch interaction
                  if (isTouchDragging) {
                    e.preventDefault();
                    return;
                  }

                  // Update drag preview position during mouse drag
                  if (canvasRef.current && draggedProduct === product.id) {
                    const canvasRect = canvasRef.current.getBoundingClientRect();
                    const previewX = e.clientX - canvasRect.left - dragOffset.x;
                    const previewY = e.clientY - canvasRect.top - dragOffset.y;

                    const constrainedPreview = {
                      x: Math.max(0, Math.min(previewX, canvasRect.width - 120 - 20)),
                      y: Math.max(0, Math.min(previewY, canvasRect.height - 140 - 20)),
                    };

                    setDragPreview(constrainedPreview);
                  }
                }}
                onDragEnd={async (e) => {
                  // Prevent mouse drag end if we're in a touch interaction
                  if (isTouchDragging) {
                    e.preventDefault();
                    return;
                  }

                  console.log('Mouse drag ended for product:', product.name);
                  console.log('Final mouse position:', { x: e.clientX, y: e.clientY });

                  // Reset visual feedback
                  e.currentTarget.style.opacity = '1';
                  e.currentTarget.style.transform = 'scale(1) rotate(0deg)';
                  e.currentTarget.style.zIndex = '1';

                  setDraggedProduct(null);
                  setDragOffset({ x: 0, y: 0 });
                  setDragPreview(null);

                  // Update product position after mouse drag
                  if (canvasRef.current) {
                    const canvasRect = canvasRef.current.getBoundingClientRect();
                    console.log('Canvas rect:', canvasRect);
                    console.log('Drag offset:', dragOffset);

                    const newX = e.clientX - canvasRect.left - dragOffset.x;
                    const newY = e.clientY - canvasRect.top - dragOffset.y;

                    console.log('Calculated new position:', { x: newX, y: newY });

                    // Ensure position is within canvas bounds
                    const position = {
                      x: Math.max(0, Math.min(newX, canvasRect.width - 120 - 20)),
                      y: Math.max(0, Math.min(newY, canvasRect.height - 140 - 20)),
                    };

                    console.log('Constrained position:', position);

                    await handleProductMove(product.id, position);
                  }
                }}
                onMouseDown={(e) => {
                  // Prevent text selection during drag
                  e.preventDefault();
                  e.stopPropagation();

                  // Only start mouse drag if not already dragging, not in touch mode, and not rotating
                  // Also check if the target is the product container itself (not a button)
                  if (!draggedProduct && !isTouchDragging && !isRotating &&
                      (e.target === e.currentTarget || !(e.target as HTMLElement).closest('button'))) {
                    console.log('Mouse down on product:', product.name);
                    setIsMouseDragging(true);
                    setDraggedProduct(product.id);

                    // Calculate offset from mouse to product position
                    const rect = e.currentTarget.getBoundingClientRect();
                    const offsetX = e.clientX - rect.left;
                    const offsetY = e.clientY - rect.top;
                    setDragOffset({ x: offsetX, y: offsetY });
                    setMouseStartPos({ x: e.clientX, y: e.clientY });

                    // Add visual feedback
                    e.currentTarget.style.transform = 'scale(1.05) rotate(2deg)';
                    e.currentTarget.style.zIndex = '1000';
                    e.currentTarget.style.boxShadow = '0 10px 25px rgba(0, 0, 0, 0.3)';
                  }
                }}
                onTouchMove={(e) => {
                  // Handle touch move for mobile devices
                  e.stopPropagation();

                  if (draggedProduct === product.id && canvasRef.current && isTouchDragging && !isRotating) {
                    const touch = e.touches[0];
                    if (touch) {
                      const canvasRect = canvasRef.current.getBoundingClientRect();
                      const newX = touch.clientX - canvasRect.left - dragOffset.x;
                      const newY = touch.clientY - canvasRect.top - dragOffset.y;

                      // Constrain to canvas bounds
                      const constrainedX = Math.max(0, Math.min(newX, canvasRect.width - 120 - 20));
                      const constrainedY = Math.max(0, Math.min(newY, canvasRect.height - 140 - 20));

                      // Update the element position directly for smooth touch feedback
                      e.currentTarget.style.left = `${constrainedX}px`;
                      e.currentTarget.style.top = `${constrainedY}px`;

                      // Add subtle vibration feedback near edges (throttled)
                      const isNearEdge = constrainedX <= 10 || constrainedY <= 10 ||
                                        constrainedX >= canvasRect.width - 210 ||
                                        constrainedY >= canvasRect.height - 190;

                      if (isNearEdge && 'vibrate' in navigator) {
                        // Throttle vibration to avoid excessive calls
                        const now = Date.now();
                        if (!e.currentTarget.dataset.lastVibrate || now - parseInt(e.currentTarget.dataset.lastVibrate) > 100) {
                          navigator.vibrate(10);
                          e.currentTarget.dataset.lastVibrate = now.toString();
                        }
                      }
                    }
                  }
                }}
                onTouchEnd={async (e) => {
                  // Handle touch end for mobile devices
                  e.stopPropagation();

                  if (draggedProduct === product.id && canvasRef.current && isTouchDragging && !isRotating) {
                    const touch = e.changedTouches[0];
                    if (touch) {
                      const canvasRect = canvasRef.current.getBoundingClientRect();
                      const newX = touch.clientX - canvasRect.left - dragOffset.x;
                      const newY = touch.clientY - canvasRect.top - dragOffset.y;

                      const position = {
                        x: Math.max(0, Math.min(newX, canvasRect.width - 120 - 20)),
                        y: Math.max(0, Math.min(newY, canvasRect.height - 140 - 20)),
                      };

                      // Reset visual feedback immediately
                      e.currentTarget.style.transform = 'scale(1) rotate(0deg)';
                      e.currentTarget.style.zIndex = '1';
                      e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';

                      // Clear vibration throttling data
                      delete e.currentTarget.dataset.lastVibrate;

                      // Provide success feedback
                      if ('vibrate' in navigator) {
                        navigator.vibrate(100);
                      }

                      console.log('Touch ended for product:', product.name, 'final position:', position);
                      await handleProductMove(product.id, position);
                    }
                  }

                  // Reset touch state - always reset to prevent stuck states
                  setDraggedProduct(null);
                  setDragOffset({ x: 0, y: 0 });
                  setIsTouchDragging(false);
                  setTouchStartTime(0);
                }}
                onTouchCancel={(e) => {
                  // Handle touch cancel (interrupted touch)
                  e.stopPropagation();

                  console.log('Touch cancelled for product:', product.name);

                  if (draggedProduct === product.id) {
                    // Reset visual feedback
                    e.currentTarget.style.transform = 'scale(1) rotate(0deg)';
                    e.currentTarget.style.zIndex = '1';
                    e.currentTarget.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.1)';

                    // Clear vibration throttling data
                    delete e.currentTarget.dataset.lastVibrate;
                  }

                  // Reset touch state - always reset to prevent stuck states
                  setDraggedProduct(null);
                  setDragOffset({ x: 0, y: 0 });
                  setIsTouchDragging(false);
                  setTouchStartTime(0);
                }}
                onDragOver={(e) => {
                  // Allow drop on other products for reordering
                  e.preventDefault();
                }}
                onDrop={(e) => {
                  // Handle dropping one product onto another
                  e.preventDefault();
                  const draggedData = e.dataTransfer.getData('application/json');
                  if (draggedData) {
                    const draggedProduct = JSON.parse(draggedData);
                    if (draggedProduct.id !== product.id) {
                      // Could implement product swapping or other interactions here
                      console.log('Dropped product on another product:', draggedProduct.name, '->', product.name);
                    }
                  }
                }}
                style={{
                  backgroundColor: isDark ? '#1e293b' : '#f9fafb',
                  borderRadius: '8px',
                  padding: '12px',
                  border: `1px solid ${isDark ? '#475569' : '#e5e7eb'}`,
                  width: '100px',
                  height: '120px',
                  cursor: draggedProduct === product.id ? 'grabbing' : 'grab',
                  position: 'absolute',
                  left: product.position.x,
                  top: product.position.y,
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  textAlign: 'center',
                  zIndex: draggedProduct === product.id ? 1000 : 1,
                  boxShadow: draggedProduct === product.id
                    ? '0 10px 25px rgba(0, 0, 0, 0.3)'
                    : '0 2px 4px rgba(0, 0, 0, 0.1)',
                  transform: `translateZ(0) rotate(${product.rotation || 0}deg) ${draggedProduct === product.id ? 'scale(1.05)' : 'scale(1)'}`,
                  transition: draggedProduct === product.id ? 'none' : (isRotating ? 'none' : 'all 0.2s ease'),
                  // Touch-specific properties
                  touchAction: 'none', // Prevent default touch behaviors
                  WebkitTouchCallout: 'none', // Disable callout on iOS
                  WebkitUserSelect: 'none', // Prevent text selection on iOS
                  userSelect: 'none', // Prevent text selection
                  WebkitTapHighlightColor: 'transparent', // Remove tap highlight on iOS
                }}
              >
                {/* Product Image Container */}
                <div style={{ position: 'relative', flex: 1, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                  {product.image ? (
                    <img
                      src={product.image}
                      alt={product.name}
                      style={{
                        width: '100%',
                        height: '100%',
                        objectFit: 'contain',
                        borderRadius: '0px',
                        backgroundColor: isDark ? '#334155' : '#f9fafb',
                        opacity: productsWithoutBackground.has(product.id) ? 0.8 : 1,
                        transition: 'opacity 0s ease',
                      }}
                    />
                  ) : (
                    <div
                      style={{
                        width: '100%',
                        height: '100%',
                        backgroundColor: isDark ? '#334155' : '#e5e7eb',
                        borderRadius: '0px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '48px',
                        opacity: productsWithoutBackground.has(product.id) ? 0.8 : 1,
                        transition: 'opacity 0.2s ease',
                      }}
                    >
                      📦
                    </div>
                  )}
                </div>

                {/* Delete Button - positioned in upper left corner */}
                <div style={{
                 position: 'absolute',
                 top: '-6px',
                 left: '-6px',
                 zIndex: 1002,
                 display: hoveredProduct === product.id ? 'block' : 'none',
               }}>
                  <button
                    onMouseDown={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      e.currentTarget.style.color = '#3b82f6';
                    }}
                    onMouseUp={(e) => {
                      e.currentTarget.style.color = '#ffffff';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.color = '#ffffff';
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      handleProductRemove(product.id);
                    }}
                    style={{
                      color: '#ffffff',
                      border: 'none',
                      borderRadius: '0px',
                      width: '20px',
                      height: '20px',
                      fontSize: '16px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      fontWeight: 'bold',
                      backgroundColor: 'transparent',
                    }}
                    title="Delete product"
                  >
                    ✕
                  </button>
                </div>

                {/* Background Removal Button - positioned in upper right corner */}
                <div style={{
                 position: 'absolute',
                 top: '-6px',
                 right: '-6px',
                 zIndex: 1002,
                 display: hoveredProduct === product.id ? 'block' : 'none',
               }}>
                  <button
                    onMouseDown={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      e.currentTarget.style.color = '#3b82f6';
                    }}
                    onMouseUp={(e) => {
                      e.currentTarget.style.color = '#ffffff';
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.color = '#ffffff';
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      handleToggleBackground(product.id);
                    }}
                    style={{
                      color: '#ffffff',
                      border: 'none',
                      borderRadius: '0px',
                      width: '20px',
                      height: '20px',
                      fontSize: '16px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: 'transparent',
                    }}
                    title={productsWithoutBackground.has(product.id) ? 'Add background' : 'Remove background'}
                  >
                    ◯
                  </button>
                </div>

                {/* Rotation Handle - positioned in bottom right corner */}
                <div style={{
                 position: 'absolute',
                 bottom: '-6px',
                 right: '-6px',
                 zIndex: 1002,
                 opacity: hoveredProduct === product.id ? 1 : 0,
                 pointerEvents: hoveredProduct === product.id ? 'auto' : 'none',
                 transition: 'opacity 0.2s ease',
               }}>
                  <button
                    data-rotation-handle={product.id}
                    onMouseDown={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      if (!(isRotating && rotatingProduct === product.id)) {
                        e.currentTarget.style.color = '#3b82f6';
                      }
                    }}
                    onMouseUp={(e) => {
                      if (!(isRotating && rotatingProduct === product.id)) {
                        e.currentTarget.style.color = '#ffffff';
                      }
                    }}
                    onPointerDown={(e) => {
                      console.log('Rotation handle pointer down for product:', product.name);
                      e.stopPropagation();
                      e.preventDefault();
                      (e.currentTarget as HTMLElement).setPointerCapture(e.pointerId);

                      if (canvasRef.current) {
                        const rect = e.currentTarget.getBoundingClientRect();
                        const centerX = rect.left + rect.width / 2;
                        const centerY = rect.top + rect.height / 2;

                        startRotation(e.clientX, e.clientY, centerX, centerY, product.id, product.rotation || 0);

                        // Add visual feedback
                        e.currentTarget.style.transform = 'scale(1.2)';
                      }
                    }}
                    onPointerMove={(e) => {
                      e.stopPropagation();
                      e.preventDefault();
                      updateRotation(e.clientX, e.clientY);
                    }}
                    onPointerUp={async (e) => {
                      console.log('Rotation handle pointer up for product:', product.name);
                      e.stopPropagation();
                      e.preventDefault();
                      (e.currentTarget as HTMLElement).releasePointerCapture(e.pointerId);
                      await stopRotation(e.clientX, e.clientY);
                    }}
                    onMouseLeave={(e) => {
                      // Handle mouse leaving the button during rotation
                      if (isRotating && rotatingProduct === product.id) {
                        console.log('Mouse left rotation button, cancelling rotation');
                        setIsRotating(false);
                        setRotatingProduct(null);
                        // Reset visual feedback
                        const button = document.querySelector(`[data-product-id="${product.id}"] button:last-child`) as HTMLElement;
                        if (button) {
                          button.style.transform = 'scale(1)';
                          button.style.color = '#ffffff';
                        }
                      } else {
                        // Handle hover effects
                        e.currentTarget.style.transform = 'scale(1)';
                        e.currentTarget.style.boxShadow = '0 2px 6px rgba(16, 185, 129, 0.3)';
                        e.currentTarget.style.color = '#ffffff';
                      }
                    }}
                    onTouchStart={(e) => {
                      console.log('Rotation handle touch start for product:', product.name);
                      e.stopPropagation();

                      if (canvasRef.current) {
                        const touch = e.touches[0];
                        const rect = e.currentTarget.getBoundingClientRect();
                        const centerX = rect.left + rect.width / 2;
                        const centerY = rect.top + rect.height / 2;

                        startRotation(touch.clientX, touch.clientY, centerX, centerY, product.id, product.rotation || 0);

                        // Add visual feedback
                        e.currentTarget.style.transform = 'scale(1.2)';

                        // Provide haptic feedback if available
                        if ('vibrate' in navigator) {
                          navigator.vibrate(50);
                        }
                      }
                    }}
                    onTouchMove={(e) => {
                      e.stopPropagation();

                      if (isRotating && rotatingProduct === product.id) {
                        const touch = e.touches[0];
                        if (touch) {
                          updateRotation(touch.clientX, touch.clientY);
                        }
                      }
                    }}
                    onTouchEnd={async (e) => {
                      console.log('Rotation handle touch end for product:', product.name);
                      e.stopPropagation();

                      if (isRotating && rotatingProduct === product.id) {
                        const touch = e.changedTouches[0];
                        await stopRotation(touch.clientX, touch.clientY);

                        // Provide success feedback
                        if ('vibrate' in navigator) {
                          navigator.vibrate(100);
                        }
                      }
                    }}
                    style={{
                      color: '#ffffff',
                      border: 'none',
                      borderRadius: '0px',
                      width: '24px',
                      height: '24px',
                      fontSize: '24px',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      touchAction: 'none',
                      userSelect: 'none',
                      WebkitUserSelect: 'none',
                      MozUserSelect: 'none',
                      backgroundColor: 'transparent',
                    }}
                    title="Rotate product"
                  >
                    ↻
                  </button>
                </div>

                {/* Touch Drag Indicator */}
                {isTouchDragging && draggedProduct === product.id && (
                  <div style={{
                    position: 'absolute',
                    top: '8px',
                    right: '60px', // Position next to buttons
                    backgroundColor: 'rgba(59, 130, 246, 0.9)',
                    borderRadius: '12px',
                    padding: '4px 8px',
                    zIndex: 1001,
                  }}>
                    <span style={{
                      color: '#ffffff',
                      fontSize: '12px',
                      fontWeight: 'bold',
                    }}>
                      ⋮⋮
                    </span>
                  </div>
                )}

                {/* Product Name */}
                <h4 style={{
                  color: isDark ? '#f1f5f9' : '#1e293b',
                  margin: '0 0 4px 0',
                  fontSize: '14px',
                  fontWeight: '600',
                  lineHeight: '1.2',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap',
                  textAlign: 'center',
                }}>
                  {product.name}
                </h4>


              </div>
            ))}

            {/* Drag preview indicator */}
            {dragPreview && draggedProduct && (
              <div style={{
                position: 'absolute',
                left: dragPreview.x,
                top: dragPreview.y,
                width: '160px',
                height: '140px',
                border: '2px dashed #10b981',
                borderRadius: '8px',
                backgroundColor: 'rgba(16, 185, 129, 0.1)',
                pointerEvents: 'none',
                zIndex: 999,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
              }}>
                <span style={{
                  color: '#10b981',
                  fontSize: '24px',
                  fontWeight: 'bold',
                }}>
                  📍
                </span>
              </div>
            )}

            {/* Drop indicator overlay */}
            {isDragOver && !draggedProduct && (
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                padding: '20px',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderRadius: '8px',
                border: '2px solid #3b82f6',
                pointerEvents: 'none',
              }}>
                <p style={{ color: '#3b82f6', fontWeight: 'bold', margin: 0, fontSize: '16px' }}>
                  🎯 Drop product here!
                </p>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default CanvasScreen;